using JangAaina.Components;
using JangAaina.Models;
using JangAaina.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.FluentUI.AspNetCore.Components;
using MudBlazor.Services;
using Syncfusion.Blazor;
using Syncfusion.Licensing;
using Microsoft.AspNetCore.Http.Features;
using OfficeOpenXml;

var builder = WebApplication.CreateBuilder(args);

// Configure EPPlus license context for EPPlus 8.0.2
// The license context is now set via configuration in appsettings.json
// No need to set it programmatically

// Add this line to configure the maximum file size
builder.Services.Configure<FormOptions>(options =>
{
    options.MultipartBodyLengthLimit = 1024 * 1024 * 15; // 15MB
});

SyncfusionLicenseProvider.RegisterLicense(
    "Mzc5NTAzNUAzMjM5MmUzMDJlMzAzYjMyMzkzYlpEbit5VUtlVFJSTXBnejRwVXpLVU1aNmxMSnEreEhxYWphSkkvQ2czMFk9");

var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();
builder.Services.AddFluentUIComponents();
builder.Services.AddSyncfusionBlazor();
builder.Services.AddMudServices();

builder.Services.AddDbContext<ApplicationDbcontext>(opt => opt.UseSqlServer(connectionString));
builder.Services.AddScoped<RateCardService>();
builder.Services.AddScoped<FOCRateCardService>();
builder.Services.AddScoped<DashboardService>();
builder.Services.AddAuthentication(Microsoft.AspNetCore.Authentication.Negotiate.NegotiateDefaults.AuthenticationScheme)
    .AddNegotiate();
builder.Services.AddAuthorization(options =>
{
    options.FallbackPolicy = options.DefaultPolicy;
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseAntiforgery();

app.UseAuthentication();
app.UseAuthorization();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode(); // Remove authorization requirement

app.Run();

@page "/setup/focratecards/create"
@page "/setup/focratecards/{FOCRateCardId:int}/edit"
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@rendermode InteractiveServer

@inject FOCRateCardService service
<style>
    td {padding:2px;}
</style>
<SfToast @ref="_toastObj"></SfToast>
<h3>FOC Rate Card Detail</h3>
<EditForm FormName="foc_rate_card_form" OnValidSubmit="SaveFOCRateCard" Model="focRateCard">
    <DataAnnotationsValidator></DataAnnotationsValidator>
    <ValidationSummary></ValidationSummary>
    <div class="row">
        <div class="col">
            <SfTextBox FloatLabelType="FloatLabelType.Always"
                       @bind-Value="focRateCard.Title"
                       Placeholder="Title">
            </SfTextBox>
        </div>
    </div>
    <div class="row mt-2">
        <div class="col-md">
            <SfDatePicker @bind-Value="focRateCard.DateFrom"
                          Placeholder="Date - From"
                          FloatLabelType="FloatLabelType.Always"
                          Format="d MMM, yyyy">
            </SfDatePicker>
        </div>
        <div class="col-md">
            <SfDatePicker @bind-Value="focRateCard.DateTo"
                          Placeholder="Date - To"
                          FloatLabelType="FloatLabelType.Always"
                          Format="d MMM, yyyy">
            </SfDatePicker>
        </div>
        <div class="col-md-2">
            <SfNumericTextBox FloatLabelType="FloatLabelType.Always"
                              Placeholder="Priority"
                              ShowClearButton="false"
                              Min="1" Max="50"
                              Decimals="0"
                              ShowSpinButton="false"
                              @bind-Value="focRateCard.Priority">
            </SfNumericTextBox>
        </div>
        <div class="col-md-2">
            <p style="height: 12px;"></p>
            <div style="display: flex; align-items: center; gap:  6px">
                <SfSwitch @bind-Checked="focRateCard.IsActive"></SfSwitch> @focRateCard.Status
            </div>
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md">
            <SfCheckBox @bind-Checked="focRateCard.AllEditions"></SfCheckBox> All Editions
            @if (focRateCard.AllEditions == false)
            {
                <SfMultiSelect AllowFiltering="true" FilterType="FilterType.Contains" 
                               FloatLabelType="FloatLabelType.Always" Placeholder="Edition" 
                               @bind-Value="@focRateCard.SelectedEditionList" DataSource="EditionList">
                    <MultiSelectFieldSettings Value="Id" Text="Title"></MultiSelectFieldSettings>
                </SfMultiSelect>
            }
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md">
            <SfCheckBox @bind-Checked="focRateCard.AllGovCategories"></SfCheckBox> All Categories (Gov)
            @if (focRateCard.AllGovCategories == false)
            {
                <SfMultiSelect AllowFiltering="true" FilterType="FilterType.Contains" EnableVirtualization="true"
                               FloatLabelType="FloatLabelType.Always" Placeholder="Gov Category"
                               @bind-Value="@focRateCard.SelectedGovCategoryList" DataSource="GovCategoryList">
                    <MultiSelectFieldSettings Value="Id" Text="Title"></MultiSelectFieldSettings>
                </SfMultiSelect>
            }
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md">
            <SfCheckBox @bind-Checked="focRateCard.AllGovAdType"></SfCheckBox> All Ad Types (Gov)
            @if (focRateCard.AllGovAdType == false)
            {
                <SfMultiSelect AllowFiltering="true" FilterType="FilterType.Contains" EnableVirtualization="true"
                               FloatLabelType="FloatLabelType.Always" Placeholder="Gov Ad Type"
                               @bind-Value="@focRateCard.SelectedGovAdTypes" DataSource="AllGovAdTypesList">
                    <MultiSelectFieldSettings Value="Id" Text="Title"></MultiSelectFieldSettings>
                </SfMultiSelect>
            }
        </div>
    </div>

    <div class="row">
        <div class="col">
            <SfButton CssClass="e-primary" type="submit">Save</SfButton>
        </div>
    </div>
</EditForm>

<style>
    .e-multiselect.e-input-group.e-control-wrapper.e-input-focus::before, .e-multiselect.e-input-group.e-control-wrapper.e-input-focus::after {
        background: #c000ff;
    }
</style>

@code {
    [SupplyParameterFromForm] public FOCRateCardDto focRateCard { get; set; } = new();
    public List<EditionDto> EditionList { get; set; } = new();
    public List<ClientCategoryDto> GovCategoryList { get; set; } = new();
    public List<AdTypeCategoryDto> AllGovAdTypesList { get; set; } = new();
    private SfToast? _toastObj;
    [Parameter] public int? FOCRateCardId { get; set; }

    private async Task SaveFOCRateCard()
    {
        var op = await service.SaveFOCRateCard(focRateCard, "jawaid");
        if (op != "OK")
        {
            var tm = new ToastModel 
            { 
                Content = op, 
                Title = "Error", 
                ShowCloseButton = true, 
                ShowProgressBar = true, 
                Timeout = 5000,
                CssClass = "e-toast-danger"
            };
            await _toastObj!.ShowAsync(tm);
        }
        else
        {
            var tm = new ToastModel 
            { 
                Content = "FOC rate card saved successfully", 
                Title = "Success", 
                ShowCloseButton = true, 
                ShowProgressBar = true, 
                Timeout = 3000,
                CssClass = "e-toast-success"
            };
            await _toastObj!.ShowAsync(tm);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        EditionList = await service.GetAllEditions();
        GovCategoryList = await service.GetAllGovCategories();
        AllGovAdTypesList = await service.GetAllGovAdType();
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        if (FOCRateCardId == null)
        {
            focRateCard = new FOCRateCardDto
            {
                DateFrom = new DateTime(DateTime.Today.Year, 1, 1),
                DateTo = new DateTime(DateTime.Today.Year, 1, 1).AddYears(1).AddDays(-1),
                Priority = 1,
                IsActive = true,
                AllEditions = true,
                AllGovCategories = true,
                AllGovAdType = true
            };
        }
        else
        {
            try
            {
                focRateCard = await service.OpenFOCRateCard(FOCRateCardId ?? 0);
            }
            catch (Exception ex)
            {
                var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, ShowProgressBar = true, Timeout = 5000 };
                await _toastObj!.ShowAsync(tm);
            }
        }
    }
}

// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace JangAaina.Models;

public partial class FocrateCard
{
    public int Id { get; set; }

    public string Title { get; set; } = null!;

    public DateTime DateFrom { get; set; }

    public DateTime DateTo { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public string? ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int Priority { get; set; }

    public bool IsActive { get; set; }

    public bool AllEditions { get; set; }

    public bool AllGovCategories { get; set; }

    public bool AllGovAdType { get; set; }

    public virtual ICollection<FocrateCardEdition> FocrateCardEditions { get; set; } = new List<FocrateCardEdition>();

    public virtual ICollection<FocrateCardGovAdType> FocrateCardGovAdTypes { get; set; } = new List<FocrateCardGovAdType>();

    public virtual ICollection<FocrateCardGovCategory> FocrateCardGovCategories { get; set; } = new List<FocrateCardGovCategory>();
}
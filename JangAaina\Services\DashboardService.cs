using JangAaina.Models;
using Microsoft.EntityFrameworkCore;
using Dapper;
using JangAaina.DTO;

namespace JangAaina.Services;

public class DashboardService
{
    private readonly ApplicationDbcontext dc;

    public DashboardService(ApplicationDbcontext dc)
    {
        this.dc = dc;
    }

    public async Task<int> GetNumberOfPublicationsAsync()
    {
        var factAdvertisingData = await dc.Set<FactAdvertising>().ToListAsync();
        return factAdvertisingData.Select(f => f.PublicationId).Distinct().Count();
    }

    public async Task<(string StartDate, string EndDate)> GetAdvertisementPeriodAsync()
    {
        // Corrected the code to handle nullable DateTime instead of DateOnly
        var dates = dc.RawData
            //.Where(f => f.Date != null) // Check for non-null values
            .Select(f => f.Date.ToDateTime(TimeOnly.MinValue)) // Convert DateOnly to DateTime
            .Distinct()
            .ToList();

        if (dates.Any())
        {
            return (dates.Min().ToString("d-MMM-yyyy"), dates.Max().ToString("d-MMM-yyyy"));
        }
        return (string.Empty, string.Empty);
    }

    public async Task<List<GovAdTypeRevenueDto>> GetGovAdTypeRevenueShareAsync()
    {
        var connection = dc.Database.GetDbConnection();
        var data = await connection.QueryAsync<GovAdTypeRevenueDto>("dbo.dashboard_GovAdTypeWiseRevenue", 
            commandType: System.Data.CommandType.StoredProcedure);
        
        var result = data.ToList();
        var totalRevenue = result.Sum(x => x.Revenue);
        
        foreach(var item in result)
        {
            item.Share = Math.Round((double)(item.Revenue / totalRevenue) * 100, 2);
        }
        
        return result;
    }

    public async Task<List<DateOnly>> GetMissingDatesAsync(DateOnly startDate, DateOnly endDate)
    {
        // Get all dates that have data in RawData table
        var existingDates = await dc.RawData
            //.Where(r => r.Date.HasValue)
            .Select(r => r.Date) // Ensure nullable value is accessed safely
            .Select(date => DateOnly.FromDateTime(date.ToDateTime(TimeOnly.MinValue))) // Convert DateTime to DateOnly
            .ToListAsync();

        // Filter dates within the range
        existingDates = existingDates
            .Where(date => date >= startDate && date <= endDate)
            .Distinct()
            .ToList();

        // Generate all dates in the range
        var allDates = new List<DateOnly>();
        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            allDates.Add(date);
        }

        // Find missing dates
        return allDates
            .Where(d => !existingDates.Contains(d))
            .OrderBy(d => d)
            .ToList();
    }
}

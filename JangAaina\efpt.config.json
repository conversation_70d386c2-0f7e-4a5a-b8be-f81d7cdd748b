﻿{
   "CodeGenerationMode": 5,
   "ContextClassName": "ApplicationDbcontext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "Models",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "JangAaina",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 2,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[dbo].[DimAdTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimBrands]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimCategories]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimCities]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimColours]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimCompanies]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimCountries]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimEditions]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimFrequencies]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimGovAdTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimGovCategories]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimIndustries]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimLanguages]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimMediaAgencies]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimPageTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimPublications]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimPublishTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DimSubPublications]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[FactAdvertising]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[FOCRateCardEditions]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[FOCRateCardGovAdTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[FOCRateCardGovCategories]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[FOCRateCards]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RateCardAdTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RateCardCategories]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RateCardCities]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RateCardClients]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RateCardDays]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RateCardEditions]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RateCardGovAdTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RateCardGovCategories]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RateCardIndustries]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RateCardPageAndColors]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RateCards]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RateCardSubPublications]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RawData]",
         "ObjectType": 0
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": true,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": true,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": true,
   "UseNoDefaultConstructor": true,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": true,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}
﻿using System.Data;
using Dapper;
using JangAaina.DTO;
using JangAaina.Models;
using Microsoft.EntityFrameworkCore;

namespace JangAaina.Services;

public class RateCardService(ApplicationDbcontext dc)
{
    public Task<List<RateCardDto>> GetAllRateCards(DateTime filterDate, int? subPublicationId, int? editionId)
    {
        var q = (from a in dc.RateCards
                 orderby a.IsActive descending, a.Title
                 where a.DateFrom <= filterDate
                       && filterDate <= a.DateTo
                       && (subPublicationId == null ||
                           a.RateCardSubPublications.Any(m => m.SubPublicationId == subPublicationId))
                       && (editionId == null || a.RateCardEditions.Any(m => m.EditionId == editionId))
                       && (a.IsDeleted == false)
                 select new RateCardDto
                 {
                     Id = a.Id,
                     Title = a.Title,
                     DateFrom = a.DateFrom,
                     DateTo = a.DateTo,
                     SubPublicationsStr = string.Join(", ",
                         a.RateCardSubPublications.Select(k => k.SubPublication.SubPublicationName).ToList()),
                     IsActive = a.IsActive,
                     EditionStr = a.AllEditions
                         ? "All"
                         : string.Join(", ", a.RateCardEditions.Select(e => e.Edition.EditionName).ToList()),
                     CitiesStr = a.AllCities
                         ? "All"
                         : string.Join(", ", a.RateCardCities.Select(e => e.City.CityName).ToList()),
                     Priority = a.Priority,
                     GovCategoryStr = a.AllGovCategories ? "All" : 
                     string.Join(", ", a.RateCardGovCategories.Select(e=>e.GovCategory.GovCategoryName).ToList() ),
                     GovAdTypeStr = a.AllGovAdTypes ? "All" :
                     string.Join(", ", a.RateCardGovAdTypes.Select(e=>e.GovAdType.GovAdTypeName).ToList())

                 }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<SubPublicationDto>> GetAllSubPublications()
    {
        var q = (from a in dc.DimSubPublications
                 orderby a.SubPublicationName
                 select new SubPublicationDto
                 {
                     Id = a.SubPublicationId,
                     Title = a.SubPublicationName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<EditionDto>> GetAllEditions()
    {
        var q = (from a in dc.DimEditions
                 orderby a.EditionName
                 select new EditionDto
                 {
                     Id = a.EditionId,
                     Title = a.EditionName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<AdTypeDto>> GetAllAdTypes()
    {
        var q = (from a in dc.DimAdTypes
                 orderby a.AdTypeName
                 select new AdTypeDto
                 {
                     Id = a.AdTypeId,
                     Title = a.AdTypeName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<ClientDto>> GetAllClients()
    {
        var q = (from a in dc.DimCompanies
                 orderby a.CompanyName
                 select new ClientDto
                 {
                     Id = a.CompanyId,
                     Title = a.CompanyName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<IndustryDto>> GetAllIndustries()
    {
        var q = (from a in dc.DimIndustries
                 orderby a.IndustryName
                 select new IndustryDto
                 {
                     Id = a.IndustryId,
                     Title = a.IndustryName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<CategoryDto>> GetAllCategories()
    {
        var q = (from a in dc.DimCategories
                 orderby a.CategoryName
                 select new CategoryDto
                 {
                     Id = a.CategoryId,
                     Title = a.CategoryName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<PageTypeDto>> GetAllPageTypes()
    {
        var q = (from a in dc.DimPageTypes
                 orderby a.PageTypeName
                 select new PageTypeDto
                 {
                     Id = a.PageTypeId,
                     Title = a.PageTypeName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<ColorDto>> GetAllColors()
    {
        var q = (from a in dc.DimColours
                 orderby a.ColourName
                 select new ColorDto
                 {
                     Id = a.ColourId,
                     Title = a.ColourName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<RateCardDetailDto>> GetRateCardDetail(int rateCardId = 0)
    {
        var pts = (from a in dc.DimPageTypes
                   orderby a.PageTypeName
                   select new RateCardDetailDto
                   {
                       PageTypeId = a.PageTypeId,
                       PageType = a.PageTypeName
                   }).ToList();


        foreach (var r in pts)
        {
            // color
            var q2 = (from a in dc.RateCardPageAndColors
                      where a.RateCardId == rateCardId && a.PageTypeId == r.PageTypeId && a.ColorId == 2 && a.Rate > 0
                      select a).FirstOrDefault();
            if (q2 != null) r.Colour = q2.Rate;
            //SPOT COLOUR
            q2 = (from a in dc.RateCardPageAndColors
                  where a.RateCardId == rateCardId && a.PageTypeId == r.PageTypeId && a.ColorId == 4 && a.Rate > 0
                  select a).FirstOrDefault();
            if (q2 != null) r.SpotColour = q2.Rate;

            // All Colors
            q2 = (from a in dc.RateCardPageAndColors
                  where a.RateCardId == rateCardId && a.PageTypeId == r.PageTypeId && a.ColorId == 1 && a.Rate > 0
                  select a).FirstOrDefault();
            if (q2 != null) r.AllColour = q2.Rate;

            //black and white

            q2 = (from a in dc.RateCardPageAndColors
                  where a.RateCardId == rateCardId && a.PageTypeId == r.PageTypeId && a.ColorId == 3 && a.Rate > 0
                  select a).FirstOrDefault();
            if (q2 != null) r.BlackNWhite = q2.Rate;
        }

        return Task.FromResult(pts);
    }

    public Task<string> SaveRateCard(RateCardDto rc, string user)
    {
        // First check for overlapping rate cards
        

        var tran = dc.Database.BeginTransaction();
        try
        {
            var cardId = rc.Id;
            if (rc.Id == 0)
            {
                var card = new RateCard
                {
                    Title = rc.Title,
                    Id = 0,
                    IsActive = rc.IsActive,
                    AllEditions = rc.AllEditions,
                    AllAdTypes = rc.AllAdTypes,
                    AllCategories = rc.AllCategories,
                    AllClients = rc.AllClients,
                    AllIndustries = rc.AllIndustries,
                    AllDays = rc.AllDays,
                    CreatedBy = user,
                    CreatedDate = DateTime.Now,
                    DateFrom = rc.DateFrom,
                    DateTo = rc.DateTo,
                    Description = "",
                    Priority = rc.Priority ?? 0,
                    PremiumPercentage = rc.PremiumPercentage,
                    AllGovCategories = rc.AllGovCategories,
                    AllGovAdTypes = rc.AllGovAdTypes,
                    AllCities = rc.AllCities
                };
                dc.RateCards.Add(card);
                dc.SaveChanges();
                cardId = card.Id;
            }
            else
            {
                var card = (from a in dc.RateCards
                            where a.Id == rc.Id
                            select a).FirstOrDefault();
                if (card == null) return Task.FromResult("Record not found");
                card.Title = rc.Title;
                card.IsActive = rc.IsActive;
                card.Description = "";
                card.AllAdTypes = rc.AllAdTypes;
                card.AllCategories = rc.AllCategories;
                card.AllClients = rc.AllClients;
                card.AllEditions = rc.AllEditions;
                card.AllIndustries = rc.AllIndustries;
                card.ModifiedBy = user;
                card.CreatedDate = DateTime.Now;
                card.DateFrom = rc.DateFrom;
                card.DateTo = rc.DateTo;
                card.AllEditions = rc.AllEditions;
                card.PremiumPercentage = rc.PremiumPercentage;
                card.AllDays = rc.AllDays;
                card.AllGovCategories = rc.AllGovCategories;
                card.AllGovAdTypes = rc.AllGovAdTypes;
                card.AllCities = rc.AllCities;
                card.Priority = rc.Priority??1;
                dc.SaveChanges();
            }

            #region Rate Card Cities

            var rcc1 = dc.RateCardCities.Where(c => c.RateCardId == cardId).ToList();
            if (rcc1.Any())
            {
                dc.RateCardCities.RemoveRange(rcc1);
                dc.SaveChanges();
            }

            #endregion

            // data cleaning




            #region Rate Card Categories
            var cacs = (from a in dc.RateCardCategories
                        where a.RateCardId == cardId
                        select a).ToList();
            if (cacs.Any())

            {
                dc.RateCardCategories.RemoveRange(cacs);
                dc.SaveChanges();
            }
            #endregion

            //clients
            var cls = (from a in dc.RateCardClients
                       where a.RateCardId == cardId
                       select a).ToList();
            if (cls.Any())
            {
                dc.RateCardClients.RemoveRange(cls);
                dc.SaveChanges();
            }

            // Days
            var days = (from a in dc.RateCardDays
                        where a.RateCardId == cardId
                        select a).ToList();
            if (days.Any())
            {
                dc.RateCardDays.RemoveRange(days);
                dc.SaveChanges();
            }

            //client category
            var ccs = (from a in dc.RateCardGovCategories
                       where a.RateCardId == cardId
                       select a).ToList();
            if (ccs.Any())
            {
                dc.RateCardGovCategories.RemoveRange(ccs);
                dc.SaveChanges();
            }

            var ads = dc.RateCardAdTypes.Where(c => c.RateCardId == cardId).ToList();
            if (ads.Any())
            {
                dc.RateCardAdTypes.RemoveRange(ads);
                dc.SaveChanges();
            }


            var cats = dc.RateCardCategories.Where(c => c.RateCardId == cardId).ToList();
            if (cats.Any())
            {
                dc.RateCardCategories.RemoveRange(cats);
                dc.SaveChanges();
            }

            var red = dc.RateCardEditions.Where(c => c.RateCardId == cardId).ToList();
            if (red.Any())
            {
                dc.RateCardEditions.RemoveRange(red);
                dc.SaveChanges();
            }

            var rci = dc.RateCardIndustries.Where(c => c.RateCardId == cardId).ToList();
            if (rci.Any())
            {
                dc.RateCardIndustries.RemoveRange(rci);
                dc.SaveChanges();
            }

            var rsp = dc.RateCardSubPublications.Where(c => c.RateCardId == cardId).ToList();
            if (rsp.Any())
            {
                dc.RateCardSubPublications.RemoveRange(rsp);
                dc.SaveChanges();
            }


            var pgs = dc.RateCardPageAndColors.Where(c => c.RateCardId == cardId).ToList();
            if (pgs.Any())
            {
                dc.RateCardPageAndColors.RemoveRange(pgs);
                dc.SaveChanges();
            }

            var ccl = dc.RateCardGovAdTypes.Where(c => c.RateCardId == cardId).ToList();
            if (ccl.Any())
            {
                dc.RateCardGovAdTypes.RemoveRange(ccl);
                dc.SaveChanges();
            }



            #region re insert data

            if (rc.AllAdTypes == false)
                foreach (var m in rc.SelectedAdTypeList)
                {
                    var kk = new RateCardAdType { AdTypeId = m.Id, RateCardId = cardId };
                    dc.RateCardAdTypes.Add(kk);
                    dc.SaveChanges();
                }

            if (rc.AllCategories == false)
                foreach (var m in rc.SelectedCategoryList)
                {
                    var kk = new RateCardCategory { CategoryId = m.Id, RateCardId = cardId };
                    dc.RateCardCategories.Add(kk);
                    dc.SaveChanges();
                }

            if (rc.AllClients == false)
                foreach (var m in rc.SelectedClientList)
                {
                    var kk = new RateCardClient { ClientId = m.Id, RateCardId = cardId };
                    dc.RateCardClients.Add(kk);
                    dc.SaveChanges();
                }

            if (rc.AllEditions == false)
                foreach (var m in rc.SelectedEditionList)
                {
                    var kk = new RateCardEdition { EditionId = m.Id, RateCardId = cardId };
                    dc.RateCardEditions.Add(kk);
                    dc.SaveChanges();
                }

            if (rc.AllDays == false)
                foreach (var dd in rc.SelectedDaysList)
                {
                    var kk = new RateCardDay { DayNumber = dd.Id, RateCardId = cardId };
                    dc.RateCardDays.Add(kk);
                    dc.SaveChanges();
                }

            if (rc.AllIndustries == false)
                foreach (var m in rc.SelectedIndustryList)
                {
                    var kk = new RateCardIndustry { IndustryId = m.Id, RateCardId = cardId };
                    dc.RateCardIndustries.Add(kk);
                    dc.SaveChanges();
                }

            if (rc.AllCities == false)
            {
                foreach (var cc in rc.SelectedCitiesList)
                {
                    var kk = new RateCardCity() { CityId = cc.Id, RateCardId = cardId };
                    dc.RateCardCities.Add(kk);
                    dc.SaveChanges();
                }
            }

            if (rc.AllGovCategories == false)
            {
                foreach (var cc in rc.SelectedGovCategoryList)
                {
                    var kk = new RateCardGovCategory() { GovCategoryId = cc.Id, RateCardId = cardId };
                    dc.RateCardGovCategories.Add(kk);
                    dc.SaveChanges();
                }
            }

            if (rc.AllGovAdTypes == false)
            {
                foreach (var cc in rc.SelectedGovAdTypes)
                {
                    var kk = new RateCardGovAdType() { GovAdTypeId = cc.Id, RateCardId = cardId };
                    dc.RateCardGovAdTypes.Add(kk);
                    dc.SaveChanges();
                }
            }

            //if (rc.AllGovCategories == false)
            //    foreach (var m in rc.SelectedGovCategoryList)
            //    {
            //        var kk = new RateCardClientCategory { ClientCategoryId = m.Id, RateCardId = cardId };
            //        dc.RateCardClientCategories.Add(kk);
            //        dc.SaveChanges();
            //    }

            //if (rc.AllGovAdTypes == false)
            //    foreach (var mm in rc.SelectedGovAdTypes)
            //    {
            //        var kk = new RateCardAdTypeCategory { AdTypeCategoryId = mm.Id, RateCardId = cardId };
            //        dc.RateCardAdTypeCategories.Add(kk);
            //        dc.SaveChanges();
            //    }

            foreach (var m in rc.SelectedSubPublicationList)
            {
                var kk = new RateCardSubPublication { SubPublicationId = m.Id, RateCardId = cardId };
                dc.RateCardSubPublications.Add(kk);
                dc.SaveChanges();
            }

            #endregion

            #region Save Rates

            foreach (var item in rc.Rates)
            {
                if (item.Colour > 0)
                {
                    var mm = new RateCardPageAndColor
                    { ColorId = 2, RateCardId = cardId, PageTypeId = item.PageTypeId, Rate = item.Colour };
                    dc.RateCardPageAndColors.Add(mm);
                    dc.SaveChanges();
                }

                if (item.SpotColour > 0)
                {
                    var mm = new RateCardPageAndColor
                    { ColorId = 4, RateCardId = cardId, PageTypeId = item.PageTypeId, Rate = item.SpotColour };
                    dc.RateCardPageAndColors.Add(mm);
                    dc.SaveChanges();
                }

                if (item.AllColour > 0)
                {
                    var mm = new RateCardPageAndColor
                    { ColorId = 1, RateCardId = cardId, PageTypeId = item.PageTypeId, Rate = item.AllColour };
                    dc.RateCardPageAndColors.Add(mm);
                    dc.SaveChanges();
                }

                if (item.BlackNWhite > 0)
                {
                    var mm = new RateCardPageAndColor
                    { ColorId = 3, RateCardId = cardId, PageTypeId = item.PageTypeId, Rate = item.BlackNWhite };
                    dc.RateCardPageAndColors.Add(mm);
                    dc.SaveChanges();
                }
            }

            #endregion
            tran.Commit();
            // scope.Complete();
            return Task.FromResult("OK");
        }
        catch (Exception e)
        {
            // ignore
            tran.Rollback();
            var msg = e.Message;
            if (e.InnerException != null) msg += " Detail: " + e.InnerException.Message;

            return Task.FromResult(msg);
        }
    }

    public Task<RateCardDto> OpenRateCard(int id)
    {
        var q = (from a in dc.RateCards
                 where a.Id == id
                 select new RateCardDto
                 {
                     Id = a.Id,
                     Title = a.Title,
                     DateFrom = a.DateFrom,
                     DateTo = a.DateTo,
                     AllCategories = a.AllCategories,
                     AllClients = a.AllClients,
                     AllIndustries = a.AllIndustries,
                     AllAdTypes = a.AllAdTypes,
                     Priority = a.Priority,
                     IsActive = a.IsActive,
                     AllEditions = a.AllEditions,
                     PremiumPercentage = a.PremiumPercentage,
                     AllDays = a.AllDays,
                     AllGovCategories = a.AllGovCategories,
                     AllGovAdTypes = a.AllGovAdTypes,
                     AllCities = a.AllCities
                 }).First();
        q.SelectedSubPublicationList = (from a in dc.RateCardSubPublications
                                        where a.RateCardId == id
                                        select new SubPublicationDto
                                        {
                                            Id = a.SubPublicationId,
                                            Title = a.SubPublication.SubPublicationName
                                        }).ToList();

        q.SelectedCategoryList = (from a in dc.RateCardCategories
                                  where a.RateCardId == id
                                  select new CategoryDto
                                  {
                                      Id = a.CategoryId,
                                      Title = a.Category.CategoryName
                                  }).ToList();

        q.SelectedClientList = (from a in dc.RateCardClients
                                where a.RateCardId == id
                                select new ClientDto
                                {
                                    Id = a.ClientId,
                                    Title = a.Client.CompanyName
                                }).ToList();

        q.SelectedEditionList = (from a in dc.RateCardEditions
                                 where a.RateCardId == id
                                 select new EditionDto
                                 {
                                     Id = a.EditionId,
                                     Title = a.Edition.EditionName
                                 }).ToList();

        q.SelectedIndustryList = (from a in dc.RateCardIndustries
                                  where a.RateCardId == id
                                  select new IndustryDto
                                  {
                                      Id = a.IndustryId,
                                      Title = a.Industry.IndustryName
                                  }).ToList();


        q.SelectedAdTypeList = (from a in dc.RateCardAdTypes
                                where a.RateCardId == id
                                orderby a.AdType.AdTypeName
                                select new AdTypeDto
                                {
                                    Id = a.AdTypeId,
                                    Title = a.AdType.AdTypeName
                                }).ToList();

        q.SelectedGovCategoryList = (from a in dc.RateCardCategories
                                     where a.RateCardId == id
                                     orderby a.Category.CategoryName
                                     select new ClientCategoryDto
                                     {
                                         Id = a.CategoryId,
                                         Title = a.Category.CategoryName
                                     }).ToList();

        q.SelectedDaysList = (from a in dc.RateCardDays
                              where a.RateCardId == id
                              select new DayDto
                              {
                                  Id = a.DayNumber,
                                  Name = a.DayNumber == 1 ? "Sunday" :
                                      a.DayNumber == 2 ? "Monday" :
                                      a.DayNumber == 3 ? "Tuesday" :
                                      a.DayNumber == 4 ? "Wednesday" :
                                      a.DayNumber == 5 ? "Thursday" :
                                      a.DayNumber == 6 ? "Friday" : "Saturday"
                              }).ToList();

        q.SelectedGovAdTypes = (from a in dc.RateCardGovAdTypes
                                where a.RateCardId == id
                                select new AdTypeCategoryDto
                                {
                                    Id = a.GovAdTypeId,
                                    Title = a.GovAdType.GovAdTypeName
                                }).ToList();

        q.SelectedCitiesList = (from a in dc.RateCardCities
                                where a.RateCardId == id
                                select new EditionDto() { Id = a.CityId, Title = a.City.CityName }).ToList();

        q.SelectedGovCategoryList = (from a in dc.RateCardGovCategories
                                     where a.RateCardId == id
                                     select new ClientCategoryDto() { Title = a.GovCategory.GovCategoryName, Id = a.GovCategoryId }).ToList();

        return Task.FromResult(q);
    }

    public Task<string> ApplyRates(DateOnly dateFrom, DateOnly dateTo)
    {
        var df = new DateTime(dateFrom.Year, dateFrom.Month, dateFrom.Day);
        var dt = new DateTime(dateTo.Year, dateTo.Month, dateTo.Day);
        var pLevels = (from a in dc.RateCards
                       where a.DateFrom <= dt && a.DateTo >= df
                                                  && a.IsActive
                       select a.Priority).ToList();
        pLevels = (from p in pLevels
                   orderby p
                   select p).ToList();
        var con = dc.Database.GetDbConnection();
        var p1 = new { dateFrom, dateTo };
        con.Execute("dbo.stg_CleanRates", p1, commandType: CommandType.StoredProcedure);

        foreach (var level in pLevels)
        {
            var pram = new { dateFrom, dateTo, priority = level };
            con.Execute("dbo.ApplyRateCards", pram, commandType: CommandType.StoredProcedure);
        }

        return Task.FromResult("OK");
    }

    public Task<List<SpotDto>> GetAdSpots(DateOnly dateFrom, DateOnly dateTo)
    {
        var q = (from a in dc.FactAdvertisings
                 where a.DateKey >= dateFrom && a.DateKey <= dateTo
                 orderby a.DateKey, a.SubPublication.SubPublicationName, a.Company.CompanyName, a.Variant, a.Size1, a.Size2

                 select new SpotDto
                 {
                     Id = a.FactAdvertisingId,
                     AdType = a.AdType.AdTypeName,
                     Amount = a.AdCost,
                     Category = a.Category.CategoryName,
                     City = a.City.CityName,
                     Colour = a.Colour.ColourName,
                     Company = a.Company.CompanyName,
                     Cost = a.AdCost,
                     Date = a.DateKey,
                     //Edition = a.Edition.Title,
                     Industry = a.Industry.IndustryName,
                     PageType = a.PageType.PageTypeName,
                     Publication = a.Publication.PublicationName,
                     //Rate = a.Rate,
                     Rate = 0,
                     Size1 = a.Size1,
                     Size2 = a.Size2,
                     SubPublication = a.SubPublication.SubPublicationName,
                     Variant = a.Variant
                 }
            ).ToList();
        return Task.FromResult(q);
    }

    public Task<List<ImportMonthDto>> GetImportMonthList()
    {
        var con = dc.Database.GetDbConnection();
        var res = con.Query<ImportMonthDto>("stg_GetMissingDataList", commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(res);
    }


    public Task<string> DoImport(int year, int month)
    {
        try
        {
            var con = dc.Database.GetDbConnection();

            // update setup tables
            con.Execute("dbo.stgUpdateSetupTables", commandType: CommandType.StoredProcedure);

            var pram = new { year, month };

            // update staging table
            con.Execute("dbo.stgUpdateStageTable", pram, commandType: CommandType.StoredProcedure);
            con.Execute("dbo.stgUpdateAdStations", pram, commandType: CommandType.StoredProcedure);

            return Task.FromResult("OK");
        }
        catch (Exception e)
        {
            var msg = e.Message;
            if (e.InnerException != null)
                msg += " - Detail: " + e.InnerException.Message;

            return Task.FromResult(msg);
            //Console.WriteLine(e);

            //throw;
        }
    }

    public Task<List<ClientCategoryDto>> GetAllGovCategories()
    {
        var q = (from a in dc.DimGovCategories
                 orderby a.GovCategoryName
                 select new ClientCategoryDto
                 {
                     Id = a.GovCategoryId,
                     Title = a.GovCategoryName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<ClientDto>> GetClients()
    {
        var q = (from a in dc.DimCompanies
                 orderby a.CompanyName.Trim()//, a.Industry.Title
                 where (a.CompanyName ?? "") != ""
                 select new ClientDto
                 {
                     Id = a.CompanyId,
                     Title = (a.CompanyName ?? "").Trim(),
                     //ClientCategoryId = a.ClientCategoryId,
                     //ClientCategory = a.ClientCategory.Title,
                     Selected = false,
                     //Industry = a.Industry.Title,
                     //SubCategory = a.SubCategory.Title,
                     //AdTypeCategory = a.ClientAdTypeCategory.Title
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<ClientCategoryDto>> GetClientCategories()
    {
        var q = (from a in dc.DimGovCategories
                 orderby a.GovCategoryName
                 select new ClientCategoryDto
                 {
                     Id = a.GovCategoryId,
                     Title = a.GovCategoryName
                 }).ToList();
        return Task.FromResult(q);
    }

    //public Task<string> ApplyCategory(List<ClientDto> clients, int? clientCategoryId)
    //{
    //    var categoryId = clientCategoryId ?? 34;
    //    foreach (var client in clients)
    //    {
    //        var c = dc.Clients.Find(client.Id);
    //        if (c != null)
    //        {
    //            c.ClientCategoryId = categoryId;
    //            if (categoryId != 25)
    //            {
    //                c.SubCategoryId = null;
    //                c.ClientAdTypeCategoryId = 1;
    //            }

    //            dc.SaveChanges();
    //        }
    //    }

    //    return Task.FromResult("OK");
    //}

    public Task<List<AdTypeCategoryDto>> GetAllGovAdType()
    {
        var q = (from a in dc.DimGovAdTypes
                 orderby a.GovAdTypeName
                 select new AdTypeCategoryDto
                 {
                     Id = a.GovAdTypeId,
                     Title = a.GovAdTypeName
                 }).ToList();
        return Task.FromResult(q);
    }

    //public Task<List<SubCategoryDto>> GetSubCategories()
    //{
    //    var q = (from a in dc.SubCategories
    //        orderby a.Title
    //        select new SubCategoryDto
    //        {
    //            Id = a.SubCategoryId,
    //            Name = a.Title
    //        }).ToList();
    //    return Task.FromResult(q);
    //}

    public Task<List<SubCategoryDto>> GetAdCategories()
    {
        var q = (from a in dc.DimGovCategories
                 orderby a.GovCategoryName
                 select new SubCategoryDto
                 {
                     Id = a.GovCategoryId,
                     Name = a.GovCategoryName
                 }).ToList();
        return Task.FromResult(q);
    }

    //public Task<string> ApplySubCategory(List<ClientDto> clients, int? subCategoryId)
    //{
    //    foreach (var client in clients)
    //    {
    //        var q = (from a in dc.Clients
    //            where a.Id == client.Id
    //            select a).FirstOrDefault();
    //        if (q is { ClientCategoryId: 25 })
    //        {
    //            q.SubCategoryId = subCategoryId;
    //            dc.SaveChanges();
    //        }
    //    }

    //    return Task.FromResult("OK");
    //}

    //public Task ApplyAdCategory(List<ClientDto> clients, int? adCategoryId)
    //{
    //    foreach (var client in clients)
    //    {
    //        var q = (from a in dc.Clients
    //            where a.Id == client.Id
    //            select a).FirstOrDefault();
    //        if (q is { ClientCategoryId: 25 })
    //        {
    //            q.ClientAdTypeCategoryId = adCategoryId;
    //            dc.SaveChanges();
    //        }
    //    }

    //    return Task.FromResult("OK");
    //}
    public Task ApplyAdCategory(List<ClientDto> clientDtos, int? selectedAdCategory)
    {
        return Task.CompletedTask;
    }

    public Task ApplySubCategory(List<ClientDto> clientDtos, int? selectedSubCategory)
    {
        return Task.CompletedTask;
    }

    public Task<List<EditionDto>> GetAllCities()
    {
        var q = (from a in dc.DimCities
                 orderby a.CityName
                 select new EditionDto()
                 {
                     Id = a.CityId,
                     Title = a.CityName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> DeleteRateCard(int rateCardId)
    {
        // mark rate card as deleted
        var rc = dc.RateCards.Find(rateCardId);
        if (rc == null) return Task.FromResult("Record not found");
        rc.IsDeleted = true;
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    public async Task<string> ImportExcelData(List<DateOnly> dates, List<RawDatum> data)
    {
        try
        {
            using var transaction = await dc.Database.BeginTransactionAsync();
            
            try
            {
                // Delete existing records for the dates
                foreach (var date in dates)
                {
                    await dc.Database.ExecuteSqlRawAsync(
                        "DELETE FROM RawData WHERE Date = {0}", 
                        date.ToDateTime(TimeOnly.MinValue));
                }

                // Import new records
                await dc.RawData.AddRangeAsync(data);
                await dc.SaveChangesAsync();

                await transaction.CommitAsync();
                return "OK";
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }
}
